/**
 * 截图工具
 * 用于捕获编辑器界面截图
 */

import html2canvas from 'html2canvas';

/**
 * 捕获屏幕截图
 * @param selector 要捕获的元素选择器，默认为整个文档
 * @param options 截图选项
 * @returns 截图的base64数据URL
 */
export const captureScreenshot = async (
  selector: string = 'body',
  options: {
    scale?: number;
    quality?: number;
    backgroundColor?: string;
    excludeElements?: string[];
  } = {}
): Promise<string> => {
  try {
    const element = document.querySelector(selector);
    
    if (!element) {
      throw new Error(`未找到元素: ${selector}`);
    }
    
    // 默认选项
    const defaultOptions = {
      scale: window.devicePixelRatio || 1,
      quality: 0.8,
      backgroundColor: null,
      excludeElements: ['.feedback-form', '.ant-modal', '.ant-drawer']
    };
    
    // 合并选项
    const mergedOptions = { ...defaultOptions, ...options };
    
    // 处理需要排除的元素
    const elementsToExclude = mergedOptions.excludeElements || [];
    const originalStyles: { [key: string]: string } = {};
    
    // 临时隐藏不需要截图的元素
    elementsToExclude.forEach(excludeSelector => {
      const elements = document.querySelectorAll(excludeSelector);
      elements.forEach((el: HTMLElement) => {
        originalStyles[excludeSelector] = el.style.display;
        el.style.display = 'none';
      });
    });
    
    // 捕获截图
    const canvas = await html2canvas(element as HTMLElement, {
      scale: mergedOptions.scale,
      backgroundColor: mergedOptions.backgroundColor,
      logging: false,
      allowTaint: true,
      useCORS: true
    });
    
    // 恢复隐藏的元素
    elementsToExclude.forEach(excludeSelector => {
      const elements = document.querySelectorAll(excludeSelector);
      elements.forEach((el: HTMLElement) => {
        el.style.display = originalStyles[excludeSelector] || '';
      });
    });
    
    // 转换为base64
    return canvas.toDataURL('image/jpeg', mergedOptions.quality);
  } catch (error) {
    console.error('截图失败:', error);
    throw error;
  }
};

/**
 * 捕获元素截图
 * @param element 要捕获的HTML元素
 * @param options 截图选项
 * @returns 截图的base64数据URL
 */
export const captureElementScreenshot = async (
  element: HTMLElement,
  options: {
    scale?: number;
    quality?: number;
    backgroundColor?: string;
  } = {}
): Promise<string> => {
  try {
    if (!element) {
      throw new Error('未提供有效的元素');
    }
    
    // 默认选项
    const defaultOptions = {
      scale: window.devicePixelRatio || 1,
      quality: 0.8,
      backgroundColor: null
    };
    
    // 合并选项
    const mergedOptions = { ...defaultOptions, ...options };
    
    // 捕获截图
    const canvas = await html2canvas(element, {
      scale: mergedOptions.scale,
      backgroundColor: mergedOptions.backgroundColor,
      logging: false,
      allowTaint: true,
      useCORS: true
    });
    
    // 转换为base64
    return canvas.toDataURL('image/jpeg', mergedOptions.quality);
  } catch (error) {
    console.error('元素截图失败:', error);
    throw error;
  }
};

/**
 * 将base64图像数据转换为Blob对象
 * @param base64 base64图像数据
 * @returns Blob对象
 */
export const base64ToBlob = (base64: string): Blob => {
  const parts = base64.split(';base64,');
  const contentType = parts[0].split(':')[1];
  const raw = window.atob(parts[1]);
  const rawLength = raw.length;
  const uInt8Array = new Uint8Array(rawLength);
  
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i);
  }
  
  return new Blob([uInt8Array], { type: contentType });
};

/**
 * 将base64图像数据转换为File对象
 * @param base64 base64图像数据
 * @param filename 文件名
 * @returns File对象
 */
export const base64ToFile = (base64: string, filename: string): File => {
  const blob = base64ToBlob(base64);
  return new File([blob], filename, { type: blob.type });
};

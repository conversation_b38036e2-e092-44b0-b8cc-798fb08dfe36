/**
 * Git服务
 * 负责管理Git版本控制功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { message } from 'antd';
import axios from 'axios';
import { store } from '../store';
import {
  setGitStatus,
  setGitBranches,
  setCurrentBranch,
  setCommitHistory,
  setUnstagedFiles,
  setStagedFiles,
  setConflictFiles,
  setIsLoading,
  setRemotes,
  setCurrentRemote,
  setIsMerging,
  setMergeConflicts
} from '../store/git/gitSlice';

// Git事件类型
export enum GitServiceEventType {
  STATUS_CHANGED = 'statusChanged',
  BRANCH_CHANGED = 'branchChanged',
  COMMIT_CREATED = 'commitCreated',
  PUSH_COMPLETED = 'pushCompleted',
  PULL_COMPLETED = 'pullCompleted',
  MERGE_STARTED = 'mergeStarted',
  MERGE_COMPLETED = 'mergeCompleted',
  MERGE_CONFLICT = 'mergeConflict',
  CONFLICT_RESOLVED = 'conflictResolved',
  ERROR = 'error'
}

// Git状态接口
export interface GitStatus {
  isRepo: boolean;
  branch: string;
  ahead: number;
  behind: number;
  staged: number;
  unstaged: number;
  untracked: number;
  conflicted: number;
  files?: GitFileStatus[];
}

// Git分支接口
export interface GitBranch {
  name: string;
  current: boolean;
  remote: boolean;
  remoteName?: string;
  remoteTrackingBranch?: string;
  isDetached: boolean;
}

// Git提交接口
export interface GitCommit {
  hash: string;
  shortHash: string;
  date: string;
  message: string;
  author: string;
  email: string;
}

// Git文件状态接口
export interface GitFileStatus {
  path: string;
  status: string;
  staged: boolean;
}

// Git冲突接口
export interface GitConflict {
  path: string;
  content: string;
  ourContent: string;
  theirContent: string;
  baseContent: string;
}

// Git远程仓库接口
export interface GitRemote {
  name: string;
  url: string;
}

// Git服务配置接口
export interface GitServiceConfig {
  apiUrl: string;
  debug?: boolean;
  autoFetch?: boolean;
  fetchInterval?: number;
}

/**
 * Git服务类
 */
class GitService extends EventEmitter {
  private static instance: GitService;
  private config: GitServiceConfig;
  private fetchTimer: NodeJS.Timeout | null = null;

  /**
   * 获取Git服务实例
   * @param config 配置
   * @returns Git服务实例
   */
  public static getInstance(config?: Partial<GitServiceConfig>): GitService {
    if (!GitService.instance) {
      GitService.instance = new GitService(config);
    }
    return GitService.instance;
  }

  /**
   * 创建Git服务实例
   * @param config 配置
   */
  private constructor(config?: Partial<GitServiceConfig>) {
    super();

    // 默认配置
    this.config = {
      apiUrl: '/api/git',
      debug: false,
      autoFetch: true,
      fetchInterval: 60000, // 1分钟
      ...config
    };

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  private initialize(): void {
    if (this.config.debug) {
      console.log('Git服务初始化');
    }

    // 设置自动获取
    if (this.config.autoFetch) {
      this.setupAutoFetch();
    }

    // 初始获取状态
    this.fetchStatus();
  }

  /**
   * 设置自动获取
   */
  private setupAutoFetch(): void {
    if (this.fetchTimer) {
      clearInterval(this.fetchTimer);
    }

    this.fetchTimer = setInterval(() => {
      this.fetchStatus();
    }, this.config.fetchInterval);
  }

  /**
   * 获取Git状态
   */
  public async fetchStatus(): Promise<void> {
    try {
      store.dispatch(setIsLoading(true));

      const response = await axios.get(`${this.config.apiUrl}/status`);
      const status: GitStatus = response.data;

      store.dispatch(setGitStatus(status));
      this.emit(GitServiceEventType.STATUS_CHANGED, status);

      // 获取分支信息
      await this.fetchBranches();

      // 获取文件状态
      await this.fetchFiles();

      if (this.config.debug) {
        console.log('Git状态获取成功', status);
      }
    } catch (error) {
      console.error('获取Git状态失败', error);
      this.emit(GitServiceEventType.ERROR, error);
      message.error('获取Git状态失败');
    } finally {
      store.dispatch(setIsLoading(false));
    }
  }

  /**
   * 获取分支信息
   */
  public async fetchBranches(): Promise<void> {
    try {
      const response = await axios.get(`${this.config.apiUrl}/branches`);
      const branches: GitBranch[] = response.data.branches;
      const currentBranch = branches.find(branch => branch.current)?.name || '';

      store.dispatch(setGitBranches(branches));
      store.dispatch(setCurrentBranch(currentBranch));

      if (this.config.debug) {
        console.log('Git分支获取成功', branches);
      }
    } catch (error) {
      console.error('获取Git分支失败', error);
      this.emit(GitServiceEventType.ERROR, error);
    }
  }

  /**
   * 获取远程仓库信息
   */
  public async fetchRemotes(): Promise<void> {
    try {
      const response = await axios.get(`${this.config.apiUrl}/remotes`);
      const remotes: GitRemote[] = response.data.remotes;
      const currentRemote = remotes.length > 0 ? remotes[0].name : '';

      store.dispatch(setRemotes(remotes));
      store.dispatch(setCurrentRemote(currentRemote));

      if (this.config.debug) {
        console.log('Git远程仓库获取成功', remotes);
      }
    } catch (error) {
      console.error('获取Git远程仓库失败', error);
      this.emit(GitServiceEventType.ERROR, error);
    }
  }

  /**
   * 获取文件状态
   */
  public async fetchFiles(): Promise<void> {
    try {
      const response = await axios.get(`${this.config.apiUrl}/files`);
      const { unstaged, staged, conflicted } = response.data;

      store.dispatch(setUnstagedFiles(unstaged));
      store.dispatch(setStagedFiles(staged));
      store.dispatch(setConflictFiles(conflicted));

      if (this.config.debug) {
        console.log('Git文件状态获取成功', { unstaged, staged, conflicted });
      }
    } catch (error) {
      console.error('获取Git文件状态失败', error);
      this.emit(GitServiceEventType.ERROR, error);
    }
  }

  /**
   * 获取提交历史
   * @param limit 限制数量
   * @param skip 跳过数量
   */
  public async fetchCommitHistory(limit: number = 50, skip: number = 0): Promise<void> {
    try {
      store.dispatch(setIsLoading(true));

      const response = await axios.get(`${this.config.apiUrl}/log`, {
        params: { limit, skip }
      });
      const commits: GitCommit[] = response.data.commits;

      store.dispatch(setCommitHistory(commits));

      if (this.config.debug) {
        console.log('Git提交历史获取成功', commits);
      }
    } catch (error) {
      console.error('获取Git提交历史失败', error);
      this.emit(GitServiceEventType.ERROR, error);
      message.error('获取Git提交历史失败');
    } finally {
      store.dispatch(setIsLoading(false));
    }
  }

  /**
   * 添加文件到暂存区
   * @param files 文件路径列表
   */
  public async addFiles(files: string[]): Promise<void> {
    try {
      store.dispatch(setIsLoading(true));

      await axios.post(`${this.config.apiUrl}/add`, { files });

      // 重新获取文件状态
      await this.fetchFiles();

      if (this.config.debug) {
        console.log('文件添加到暂存区成功', files);
      }
    } catch (error) {
      console.error('添加文件到暂存区失败', error);
      this.emit(GitServiceEventType.ERROR, error);
      message.error('添加文件到暂存区失败');
      throw error;
    } finally {
      store.dispatch(setIsLoading(false));
    }
  }

  /**
   * 提交更改
   * @param commitMessage 提交消息
   */
  public async commit(commitMessage: string): Promise<void> {
    try {
      store.dispatch(setIsLoading(true));

      const response = await axios.post(`${this.config.apiUrl}/commit`, { message: commitMessage });
      const commit: GitCommit = response.data.commit;

      // 重新获取状态
      await this.fetchStatus();

      this.emit(GitServiceEventType.COMMIT_CREATED, commit);

      if (this.config.debug) {
        console.log('提交成功', commit);
      }
    } catch (error) {
      console.error('提交失败', error);
      this.emit(GitServiceEventType.ERROR, error);
      message.error('提交失败');
      throw error;
    } finally {
      store.dispatch(setIsLoading(false));
    }
  }
}

// 导出Git服务实例
export const gitService = GitService.getInstance();

export default GitService;
